#!/usr/bin/env node

/**
 * 🧪 Airtable Local Test Script
 * 
 * This script tests your Airtable configuration locally using the exact same
 * field mappings and data structure as your production code.
 * 
 * Usage:
 *   1. Set your environment variables or edit the config section below
 *   2. Run: node test-airtable-local.js
 *   3. Or run with custom values: AIRTABLE_KEY=xxx AIRTABLE_BASE=yyy node test-airtable-local.js
 */

import fetch from 'node-fetch';

// 🔧 Configuration - Edit these values or use environment variables
const CONFIG = {
  // Option 1: Use environment variables (recommended)
  AIRTABLE_KEY: process.env.AIRTABLE_KEY || process.env.AIRTABLE_API_KEY || '',
  AIRTABLE_BASE: process.env.AIRTABLE_BASE || process.env.AIRTABLE_BASE_ID || '',
  
  // Option 2: Hardcode for testing (NOT recommended for production)
  // AIRTABLE_KEY: 'patXXXXXXXXXXXXXX.xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
  // AIRTABLE_BASE: 'appXXXXXXXXXXXXXX',
  
  TABLE_NAME: 'Prospects',
  
  // Test data that matches your form structure
  TEST_DATA: {
    name: 'Test User Local',
    company: 'Test Company Local',
    email: '<EMAIL>',
    phone: '+33123456789',
    status: 'optimisation',
    context: 'Test local de la configuration Airtable',
    utm_source: 'test-local',
    utm_campaign: 'debug-local'
  }
};

// 🎨 Console styling
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  console.log('\n' + '='.repeat(60));
  log(`🧪 ${title}`, 'cyan');
  console.log('='.repeat(60));
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// 🔍 Validation functions
function validateConfig() {
  logSection('Configuration Validation');
  
  const issues = [];
  
  if (!CONFIG.AIRTABLE_KEY) {
    issues.push('AIRTABLE_KEY is missing');
  } else {
    logSuccess(`AIRTABLE_KEY found (length: ${CONFIG.AIRTABLE_KEY.length})`);
    
    // Validate key format
    if (CONFIG.AIRTABLE_KEY.startsWith('pat')) {
      logSuccess('AIRTABLE_KEY uses correct Personal Access Token format');
    } else if (CONFIG.AIRTABLE_KEY.startsWith('key')) {
      logWarning('AIRTABLE_KEY uses deprecated API key format - consider upgrading to Personal Access Token');
    } else {
      logWarning('AIRTABLE_KEY format not recognized');
    }
  }
  
  if (!CONFIG.AIRTABLE_BASE) {
    issues.push('AIRTABLE_BASE is missing');
  } else {
    logSuccess(`AIRTABLE_BASE found (${CONFIG.AIRTABLE_BASE})`);
    
    if (CONFIG.AIRTABLE_BASE.startsWith('app') && CONFIG.AIRTABLE_BASE.length === 17) {
      logSuccess('AIRTABLE_BASE format looks correct');
    } else {
      logWarning('AIRTABLE_BASE format might be incorrect (should start with "app" and be 17 characters)');
    }
  }
  
  if (issues.length > 0) {
    logError('Configuration issues found:');
    issues.forEach(issue => logError(`  - ${issue}`));
    logInfo('Set environment variables: AIRTABLE_KEY and AIRTABLE_BASE');
    logInfo('Or edit the CONFIG section in this script');
    return false;
  }
  
  logSuccess('Configuration validation passed!');
  return true;
}

// 🔗 Test Airtable connection
async function testConnection() {
  logSection('Airtable Connection Test');
  
  try {
    const url = `https://api.airtable.com/v0/${CONFIG.AIRTABLE_BASE}`;
    logInfo(`Testing connection to: ${url}`);
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${CONFIG.AIRTABLE_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      logSuccess('Successfully connected to Airtable base');
      return true;
    } else {
      const errorData = await response.json().catch(() => ({}));
      logError(`Connection failed with status ${response.status}`);
      logError(`Error details: ${JSON.stringify(errorData, null, 2)}`);
      return false;
    }
  } catch (error) {
    logError(`Connection error: ${error.message}`);
    return false;
  }
}

// 📋 Test table access
async function testTableAccess() {
  logSection('Table Access Test');
  
  try {
    const url = `https://api.airtable.com/v0/${CONFIG.AIRTABLE_BASE}/${encodeURIComponent(CONFIG.TABLE_NAME)}?maxRecords=1`;
    logInfo(`Testing access to table: ${CONFIG.TABLE_NAME}`);
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${CONFIG.AIRTABLE_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      logSuccess(`Successfully accessed table "${CONFIG.TABLE_NAME}"`);
      logInfo(`Table contains ${data.records ? data.records.length : 0} records (showing max 1)`);
      
      if (data.records && data.records.length > 0) {
        const sampleRecord = data.records[0];
        logInfo('Sample record fields:');
        Object.keys(sampleRecord.fields || {}).forEach(field => {
          logInfo(`  - ${field}`);
        });
      }
      
      return true;
    } else {
      const errorData = await response.json().catch(() => ({}));
      logError(`Table access failed with status ${response.status}`);
      logError(`Error details: ${JSON.stringify(errorData, null, 2)}`);
      
      if (response.status === 404) {
        logWarning(`Table "${CONFIG.TABLE_NAME}" might not exist or you don't have access to it`);
      }
      
      return false;
    }
  } catch (error) {
    logError(`Table access error: ${error.message}`);
    return false;
  }
}

// 📝 Create test record (matches production field mapping)
async function createTestRecord() {
  logSection('Test Record Creation');
  
  // Use the EXACT same field mapping as the Netlify function
  const fields = {
    "Nom": CONFIG.TEST_DATA.name || '',
    "Entreprise": CONFIG.TEST_DATA.company || '',
    "Email": CONFIG.TEST_DATA.email || '',
    "Téléphone": CONFIG.TEST_DATA.phone || '',
    "Statut": CONFIG.TEST_DATA.status || '',
    "Contexte": CONFIG.TEST_DATA.context || '',
    "utm_source": CONFIG.TEST_DATA.utm_source || '',
    "utm_campaign": CONFIG.TEST_DATA.utm_campaign || '',
    "Date": new Date().toISOString()
  };
  
  logInfo('Creating test record with fields:');
  Object.entries(fields).forEach(([key, value]) => {
    logInfo(`  ${key}: "${value}"`);
  });
  
  try {
    const url = `https://api.airtable.com/v0/${CONFIG.AIRTABLE_BASE}/${encodeURIComponent(CONFIG.TABLE_NAME)}`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${CONFIG.AIRTABLE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        records: [{ fields }]
      })
    });
    
    const responseData = await response.json();
    
    if (response.ok) {
      logSuccess(`Test record created successfully!`);
      logSuccess(`Record ID: ${responseData.records[0].id}`);
      logInfo('Record details:');
      console.log(JSON.stringify(responseData, null, 2));
      return true;
    } else {
      logError(`Record creation failed with status ${response.status}`);
      logError('Response details:');
      console.log(JSON.stringify(responseData, null, 2));
      
      // Provide specific guidance based on error type
      if (responseData.error && responseData.error.type === 'INVALID_PERMISSIONS_OR_MODEL_NOT_FOUND') {
        logWarning('This error suggests one of the following issues:');
        logWarning('  1. The table "Prospects" does not exist in your base');
        logWarning('  2. Your API key does not have permission to write to this table');
        logWarning('  3. One or more field names do not match your Airtable schema');
        logWarning('  4. Your base ID is incorrect');
      }
      
      return false;
    }
  } catch (error) {
    logError(`Record creation error: ${error.message}`);
    return false;
  }
}

// 🏃 Main test runner
async function runTests() {
  log('🧪 Airtable Local Test Script', 'bright');
  log('Testing the exact same configuration as your production code\n', 'cyan');
  
  const results = {
    config: false,
    connection: false,
    tableAccess: false,
    recordCreation: false
  };
  
  // Step 1: Validate configuration
  results.config = validateConfig();
  if (!results.config) {
    logError('Configuration validation failed. Please fix the issues above.');
    process.exit(1);
  }
  
  // Step 2: Test connection
  results.connection = await testConnection();
  if (!results.connection) {
    logError('Connection test failed. Check your AIRTABLE_KEY and AIRTABLE_BASE.');
    process.exit(1);
  }
  
  // Step 3: Test table access
  results.tableAccess = await testTableAccess();
  if (!results.tableAccess) {
    logError('Table access test failed. Check your table name and permissions.');
    process.exit(1);
  }
  
  // Step 4: Create test record
  results.recordCreation = await createTestRecord();
  
  // Summary
  logSection('Test Summary');
  Object.entries(results).forEach(([test, passed]) => {
    if (passed) {
      logSuccess(`${test}: PASSED`);
    } else {
      logError(`${test}: FAILED`);
    }
  });
  
  if (Object.values(results).every(result => result)) {
    logSuccess('\n🎉 All tests passed! Your Airtable configuration is working correctly.');
    logInfo('Your production endpoints should now work with these credentials.');
  } else {
    logError('\n💥 Some tests failed. Please address the issues above.');
    process.exit(1);
  }
}

// Run the tests
runTests().catch(error => {
  logError(`Unexpected error: ${error.message}`);
  console.error(error);
  process.exit(1);
});
