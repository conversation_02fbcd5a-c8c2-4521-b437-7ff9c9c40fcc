# 🧪 Airtable Configuration Test Tools

This directory contains comprehensive test tools to help you debug and validate your Airtable configuration locally, without needing to deploy to staging for each test iteration.

## 🎯 Purpose

These tools help you diagnose the `INVALID_PERMISSIONS_OR_MODEL_NOT_FOUND` error by testing:
- Airtable API connection and authentication
- Table access permissions
- Field mapping compatibility
- Record creation with exact production data structure

## 📁 Test Files

### 1. `test-airtable-local.js` - Node.js Command Line Test
A comprehensive Node.js script that tests your Airtable configuration using the exact same field mappings as your production code.

**Features:**
- ✅ Configuration validation
- ✅ Connection testing
- ✅ Table access verification
- ✅ Test record creation
- ✅ Detailed error reporting
- ✅ Matches production field mapping exactly

### 2. `test-airtable-browser.html` - Browser-Based Test
An interactive HTML page that provides the same testing capabilities in your browser.

**Features:**
- 🌐 No Node.js required
- 🔧 Interactive configuration form
- 📊 Real-time test results
- 🎨 User-friendly interface
- 📋 Visual field mapping guide

## 🚀 How to Use

### Option 1: Node.js Command Line (Recommended)

1. **Set your environment variables:**
   ```bash
   # Windows (PowerShell)
   $env:AIRTABLE_KEY="pat..."
   $env:AIRTABLE_BASE="app..."
   
   # Windows (Command Prompt)
   set AIRTABLE_KEY=pat...
   set AIRTABLE_BASE=app...
   
   # macOS/Linux
   export AIRTABLE_KEY="pat..."
   export AIRTABLE_BASE="app..."
   ```

2. **Run the test:**
   ```bash
   npm run test:airtable
   # or
   node test-airtable-local.js
   ```

3. **Or run with inline environment variables:**
   ```bash
   AIRTABLE_KEY=pat... AIRTABLE_BASE=app... node test-airtable-local.js
   ```

### Option 2: Browser Interface

1. **Open the test page:**
   ```bash
   # Serve locally (if you have a local server)
   npx serve .
   # Then open: http://localhost:3000/test-airtable-browser.html
   
   # Or simply open the file directly in your browser
   open test-airtable-browser.html
   ```

2. **Fill in your credentials** in the form
3. **Run the tests** using the buttons

## 🔑 Required Credentials

### Airtable API Key
- **Personal Access Token (Recommended):** Starts with `pat...`
- **Legacy API Key:** Starts with `key...` (deprecated but still works)

**Where to find it:**
1. Go to [Airtable Account](https://airtable.com/account)
2. Click "Generate API key" or "Personal access tokens"
3. Create a new token with appropriate permissions

### Airtable Base ID
- **Format:** Starts with `app` and is 17 characters long (e.g., `appXXXXXXXXXXXXXX`)

**Where to find it:**
1. Go to your Airtable base
2. Click "Help" → "API documentation"
3. The base ID is shown in the URL and documentation

## 📋 Required Table Structure

Your Airtable table must have these **exact** field names:

| Field Name | Type | Required | Description |
|------------|------|----------|-------------|
| `Nom` | Single line text | ✅ | Contact name |
| `Entreprise` | Single line text | ✅ | Company name |
| `Email` | Email | ✅ | Email address |
| `Téléphone` | Single line text | ❌ | Phone number |
| `Statut` | Single select | ❌ | Status (reprise, optimisation, autre) |
| `Contexte` | Long text | ❌ | Additional context |
| `utm_source` | Single line text | ❌ | UTM source |
| `utm_campaign` | Single line text | ❌ | UTM campaign |
| `Date` | Date | ❌ | Auto-populated timestamp |

## 🔍 Common Issues and Solutions

### ❌ `INVALID_PERMISSIONS_OR_MODEL_NOT_FOUND`

This error typically means:

1. **Wrong API format:** ⚠️ **MOST COMMON** - Not using the correct `records` array format
2. **Table doesn't exist:** Make sure you have a table named exactly `Prospects`
3. **Wrong base ID:** Double-check your base ID starts with `app` and is 17 characters
4. **Insufficient permissions:** Your API key needs read/write access to the table
5. **Field name mismatch:** Field names are case-sensitive and must match exactly
6. **Wrong API key:** Make sure you're using the correct API key for this base

### 🚨 Critical Fix Applied

**The main issue was using the wrong Airtable API format!**

❌ **Wrong (old format):**
```json
{ "fields": { "Nom": "...", "Email": "..." } }
```

✅ **Correct (new format):**
```json
{ "records": [{ "fields": { "Nom": "...", "Email": "..." } }] }
```

This has been fixed in all files:
- `netlify/functions/lead.mjs`
- `api/lead.js`
- `test-airtable-local.js`
- `test-airtable-browser.html`

### ❌ Connection Failed

- Check your internet connection
- Verify your API key is valid and not expired
- Ensure your base ID is correct

### ❌ Table Access Failed

- Verify the table name is exactly `Prospects` (case-sensitive)
- Check that your API key has permissions for this specific table
- Make sure the table exists in the specified base

## 🎯 Expected Test Results

### ✅ All Tests Pass
```
✅ Configuration validation passed!
✅ Successfully connected to Airtable base
✅ Successfully accessed table "Prospects"
✅ Test record created successfully!
🎉 All tests passed! Your Airtable configuration is working correctly.
```

### ❌ Tests Fail
The scripts provide detailed error messages and suggestions for each type of failure.

## 🔧 Troubleshooting Tips

1. **Start with the Node.js version** - it provides more detailed error information
2. **Test one step at a time** - use individual test buttons to isolate issues
3. **Check field names carefully** - they must match exactly (case-sensitive)
4. **Verify permissions** - make sure your API key can read and write to the table
5. **Test with a simple record first** - use the default test data before customizing

## 🔗 Production Integration

Once your tests pass, your production endpoints should work with the same credentials:
- Netlify function: `/.netlify/functions/lead`
- Fallback API: `/api/lead` (redirects to Netlify function)

## 📞 Need Help?

If tests continue to fail:
1. Check the detailed error messages in the test output
2. Verify your Airtable base structure matches the requirements
3. Test with a fresh API key to rule out permission issues
4. Try creating a test record manually in Airtable to verify field names
