# Configuration Netlify pour site statique avec structure dist
[build]
  # <PERSON><PERSON><PERSON> le répertoire dist avec le contenu statique
  command = "mkdir -p dist && cp -r *.html *.js *.css *.jpg *.png *.svg *.ico *.txt *.md _redirects netlify dist/ 2>/dev/null || true"
  # Publier depuis le répertoire dist (requis par la détection Vite)
  publish = "dist"
  # Ignorer les changements dans node_modules pour éviter les rebuilds inutiles
  ignore = "git diff --quiet $CACHED_COMMIT_REF $COMMIT_REF -- package.json package-lock.json"

[build.environment]
  NODE_VERSION = "18"

[functions]
  # Répertoire contenant les fonctions Netlify
  directory = "netlify/functions"

# Redirections pour compatibilité API
[[redirects]]
  from = "/api/lead"
  to = "/.netlify/functions/lead"
  status = 200

# Headers de sécurité
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# Configuration par environnement
[context.production]
  [context.production.environment]
    NODE_ENV = "production"

[context.deploy-preview]
  [context.deploy-preview.environment]
    NODE_ENV = "staging"

[context.branch-deploy]
  [context.branch-deploy.environment]
    NODE_ENV = "staging"
