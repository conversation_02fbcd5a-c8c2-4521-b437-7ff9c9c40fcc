<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Staging Detection</title>
</head>
<body>
    <h1>Test Staging Detection</h1>
    <div id="results"></div>

    <script>
        (function() {
            /**
             * Test the staging environment detection logic
             */
            function testStagingDetection() {
                const results = document.getElementById('results');
                
                // Test different hostnames
                const testCases = [
                    'staging.hai-systems.com',
                    'deploy-preview-123--hai-systems.netlify.app',
                    'hai-systems.netlify.app',
                    'localhost:3000',
                    '127.0.0.1:8080',
                    'hai-systems.com',
                    'www.hai-systems.com'
                ];
                
                results.innerHTML = '<h2>Staging Detection Test Results:</h2>';
                
                testCases.forEach(hostname => {
                    const isStaging = hostname.includes('staging') || 
                                     hostname.includes('deploy-preview') || 
                                     hostname.includes('netlify.app') ||
                                     hostname.includes('localhost') ||
                                     hostname.includes('127.0.0.1');
                    
                    const status = isStaging ? '🚫 STAGING' : '✅ PRODUCTION';
                    results.innerHTML += `<p><strong>${hostname}</strong>: ${status}</p>`;
                });
                
                // Test current hostname
                const currentHostname = window.location.hostname;
                const currentIsStaging = currentHostname.includes('staging') || 
                                        currentHostname.includes('deploy-preview') || 
                                        currentHostname.includes('netlify.app') ||
                                        currentHostname.includes('localhost') ||
                                        currentHostname.includes('127.0.0.1');
                
                const currentStatus = currentIsStaging ? '🚫 STAGING' : '✅ PRODUCTION';
                results.innerHTML += `<hr><p><strong>Current hostname (${currentHostname})</strong>: ${currentStatus}</p>`;
                
                // Check for robots meta tag
                const robotsTag = document.querySelector('meta[name="robots"]');
                if (robotsTag) {
                    results.innerHTML += `<p><strong>Robots meta tag</strong>: ${robotsTag.content}</p>`;
                } else {
                    results.innerHTML += `<p><strong>Robots meta tag</strong>: Not found</p>`;
                }
            }
            
            // Run test when page loads
            testStagingDetection();
        })();
    </script>
</body>
</html>
