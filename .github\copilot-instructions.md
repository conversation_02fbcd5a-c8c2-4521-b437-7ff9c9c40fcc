# Copilot Instructions for HAI-Systems-Landing-Page

## Overview
This repository contains the codebase for the HAI Systems Landing Page. The project is built using modern web development tools and frameworks, including React, TypeScript, TailwindCSS, and Vite. The purpose of this document is to guide AI coding agents in understanding the architecture, workflows, and conventions of this project to ensure productive contributions.

## Architecture
- **Frontend**: The application is a React-based single-page application (SPA) written in TypeScript. Key files include:
  - `src/App.tsx`: Main application component.
  - `src/main.tsx`: Entry point for the React application.
  - `src/index.css`: Global styles, primarily configured for TailwindCSS.
- **Backend Integration**: The `api/` directory contains backend-related scripts, such as `api/lead.js`, which likely handles lead-related operations.
- **Templates**: The `templates/` directory contains text templates (e.g., `call-script.txt`, `email-confirmation.txt`) used for communication or automation.

## Developer Workflows
### Build
- The project uses <PERSON>ite as the build tool. The configuration file is `vite.config.ts`.
- To build the project, use the following command:
  ```bash
  npm run build
  ```

### Development
- Start the development server with:
  ```bash
  npm run dev
  ```
- The development server supports hot module replacement (HMR) for a smooth development experience.

### Testing
- While no explicit test framework is mentioned, ensure any added tests align with the project's structure and dependencies.

### Linting
- The project uses ESLint for linting. The configuration file is `eslint.config.js`.
- Run linting with:
  ```bash
  npm run lint
  ```

## Conventions and Patterns
- **TypeScript**: All new code should be written in TypeScript. Type definitions should be added where applicable.
- **TailwindCSS**: Use utility classes for styling. Global styles are defined in `src/index.css`.
- **File Structure**: Follow the existing structure for organizing components, APIs, and templates.
- **Vite**: Use Vite-specific features for optimizing the build and development process.

## External Dependencies
- **React**: Core library for building the user interface.
- **TailwindCSS**: Utility-first CSS framework for styling.
- **Vite**: Build tool for modern web projects.

## Integration Points
- **API**: Ensure any new API integrations are added to the `api/` directory and follow the existing patterns.
- **Templates**: Update or add templates in the `templates/` directory as needed for communication or automation.

## Key Files and Directories
- `src/`: Contains the main application code.
- `api/`: Backend-related scripts.
- `templates/`: Text templates for communication or automation.
- `vite.config.ts`: Vite configuration.
- `eslint.config.js`: ESLint configuration.
- `tailwind.config.js`: TailwindCSS configuration.

## Notes for AI Agents
- Follow the existing coding style and conventions.
- Ensure any changes are thoroughly tested and linted.
- Document any new features or updates in the `changelog.md` file.
- If unsure about a pattern or convention, refer to similar implementations in the codebase.

---

For any questions or clarifications, consult the project maintainers or refer to the existing codebase for examples.