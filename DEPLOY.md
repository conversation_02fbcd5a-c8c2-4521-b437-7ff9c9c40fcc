# Note de Déploiement - HAI Systems Landing Page

## 1. Déploiement sur Vercel (ou Netlify)
- Pousser le repository sur GitHub : `git push origin main`
- Créer un nouveau projet sur Vercel ou Netlify en liant le repository GitHub
- Sélectionner le framework (Vite/React) et confirmer la configuration par défaut

## 2. Variables d'environnement
Dans le dashboard du projet, ajouter les variables suivantes :
- `AIRTABLE_KEY` : [insérer la valeur de votre clé API Airtable]
- `AIRTABLE_BASE` : [insérer l'ID de votre base Airtable]

**Note :** Le système supporte aussi les noms alternatifs `AIRTABLE_API_KEY` et `AIRTABLE_BASE_ID` pour la compatibilité.

## 3. (Optionnel) Configuration GA4
Remplacer `G-XXXXXXX` par votre ID GA4 réel dans le fichier `index.html` (ligne contenant le script Google Analytics).

## 4. Commande de déploiement
- Pour déployer : `git push origin main`
- Vercel/Netlify déclenchera automatiquement le build et le déploiement

## 5. Test rapide post-déploiement
- Accéder au site déployé
- Soumettre le formulaire de contact
- Vérifier qu'une nouvelle ligne apparaît dans votre base Airtable (table "Prospects")
- Vérifier que les événements sont trackés dans Google Analytics 4

## 6. Dépannage
### Erreur "Lead endpoint error"
- Vérifier que les variables d'environnement `AIRTABLE_API_KEY` et `AIRTABLE_BASE_ID` sont correctement configurées
- S'assurer que la table "Leads" existe dans votre base Airtable
- Vérifier que les champs suivants existent dans la table : Nom complet, Entreprise, Email, Téléphone, Statut, Contexte, UTM Source, UTM Campaign, Date

### Formulaire ne s'affiche pas correctement
- Vérifier que le fichier `netlify.toml` est présent à la racine du projet
- S'assurer que les fonctions Netlify sont correctement déployées dans le dossier `netlify/functions/`
