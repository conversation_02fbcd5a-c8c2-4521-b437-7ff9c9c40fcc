// api/lead.js
import fetch from 'node-fetch';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', 'POST');
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { name, company, email, phone, status, context, utm_source, utm_campaign } = req.body || {};

    if (!name || !company || !email) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    const AIRTABLE_BASE = process.env.AIRTABLE_BASE_ID; // ex : appXXXXXXXX
    const AIRTABLE_KEY = process.env.AIRTABLE_API_KEY;   // secret
    const TABLE = 'Leads';

    if (!AIRTABLE_BASE || !AIRTABLE_KEY) {
      console.error('Missing Airtable env vars');
      return res.status(500).json({ error: 'Server misconfiguration' });
    }

    const payload = {
      records: [{
        fields: {
          "Nom complet": name,
          "Entreprise": company,
          "Email": email,
          "Téléphone": phone || '',
          "Statut": status || '',
          "Contexte": context || '',
          "UTM Source": utm_source || '',
          "UTM Campaign": utm_campaign || '',
          "Date": new Date().toISOString()
        }
      }]
    };

    const r = await fetch(`https://api.airtable.com/v0/${AIRTABLE_BASE}/${encodeURIComponent(TABLE)}`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${AIRTABLE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });

    const json = await r.json();
    if (!r.ok) {
      console.error('Airtable response error', json);
      return res.status(502).json({ error: 'Airtable error', details: json });
    }

    return res.status(200).json({ ok: true, id: json.records[0].id });
  } catch (err) {
    console.error('Server error', err);
    return res.status(500).json({ error: 'Server error' });
  }
}
