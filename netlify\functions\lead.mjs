// netlify/functions/lead.mjs - Modern Netlify Functions API
export default async function(req, context) {
  console.log('🚀 Netlify function called:', {
    method: req.method,
    url: req.url,
    headers: Object.fromEntries(req.headers.entries())
  });

  if (req.method !== 'POST') {
    return new Response('Method Not Allowed', { status: 405 });
  }

  try {
    const payload = await req.json();
    console.log('📦 Received payload:', payload);

    // anti-spam simple (honeypot & timing)
    const honeypot = payload.hp || ''; // champ invisible dans le form
    const ts = payload.ts ? Number(payload.ts) : 0;
    const now = Date.now();

    if (honeypot && honeypot.trim() !== '') {
      return new Response(JSON.stringify({ error: 'Spam detected' }), { 
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    if (ts && (now - ts) < 2000) { // moins de 2s -> probablement bot
      return new Response(JSON.stringify({ error: 'Spam timing' }), { 
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // sanitize/whitelist fields - aligned with local API
    const fields = {
      "Nom complet": payload.name || '',
      "Entreprise": payload.company || '',
      "Email": payload.email || '',
      "Téléphone": payload.phone || '',
      "Statut": payload.status || '',
      "Contexte": payload.context || '',
      "UTM Source": payload.utm_source || '',
      "UTM Campaign": payload.utm_campaign || '',
      "Date": new Date().toISOString()
    };

    // Basic validation
    if (!fields["Nom complet"] || !fields["Email"] || !fields["Entreprise"]) {
      return new Response(JSON.stringify({ error: 'Missing required fields' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get environment variables
    const AIRTABLE_KEY = Netlify.env.get('AIRTABLE_API_KEY');
    const AIRTABLE_BASE = Netlify.env.get('AIRTABLE_BASE_ID');

    console.log('🔑 Environment variables check:', {
      hasAirtableKey: !!AIRTABLE_KEY,
      hasAirtableBase: !!AIRTABLE_BASE,
      keyLength: AIRTABLE_KEY ? AIRTABLE_KEY.length : 0,
      baseLength: AIRTABLE_BASE ? AIRTABLE_BASE.length : 0
    });

    if (!AIRTABLE_KEY || !AIRTABLE_BASE) {
      console.error('Missing Airtable env vars');
      return new Response(JSON.stringify({ error: 'Server config error' }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const res = await fetch(`https://api.airtable.com/v0/${AIRTABLE_BASE}/Leads`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${AIRTABLE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        records: [{ fields }]
      })
    });

    const json = await res.json();

    if (!res.ok) {
      console.error('Airtable error', json);
      return new Response(JSON.stringify({ error: 'Airtable error', details: json }), { 
        status: res.status || 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    console.log('✅ Success! Created record:', json.records[0].id);
    return new Response(JSON.stringify({ success: true, id: json.records[0].id }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (err) {
    console.error('❌ Function error:', err);
    return new Response(JSON.stringify({ error: 'Server error', details: err.message }), { 
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
